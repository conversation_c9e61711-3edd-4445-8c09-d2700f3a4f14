// Test script untuk memverifikasi bahwa count subkategori IKUT BERUBAH ketika filter lain diterapkan
// Jalankan di browser console untuk debugging

console.log('🎯 TESTING FACET COUNT DYNAMIC BEHAVIOR');

// Simulasi data produk lengkap (allProducts)
const allProducts = [
  // Konsol Game products
  { id: 1, name: "PlayStation 5", category: "Konsol Game", price: "Rp 7.999.000", rating: 4.8, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 2, name: "Xbox Series X", category: "Konsol Game", price: "Rp 7.499.000", rating: 4.7, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 3, name: "Nintendo Switch OLED", category: "Konsol Game", price: "Rp 4.999.000", rating: 4.9, shipping: "Gratis Ongkir", isMall: true, cod: true },
  { id: 4, name: "Steam Deck", category: "Konsol Game", price: "Rp 8.999.000", rating: 4.6, shipping: "Gratis Ongkir", isMall: false, cod: false },
  { id: 5, name: "PlayStation 4 Pro", category: "Konsol Game", price: "Rp 4.299.000", rating: 4.5, shipping: "Gratis Ongkir", isMall: true, cod: true },
  
  // TV & Audio products
  { id: 6, name: "Samsung Smart TV 55\"", category: "TV & Audio", price: "Rp 8.999.000", rating: 4.7, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 7, name: "Sony Soundbar", category: "TV & Audio", price: "Rp 2.999.000", rating: 4.6, shipping: "Gratis Ongkir", isMall: true, cod: true },
  { id: 8, name: "LG OLED TV 65\"", category: "TV & Audio", price: "Rp 15.999.000", rating: 4.8, shipping: "Gratis Ongkir", isMall: true, cod: false },
  
  // Kamera products
  { id: 9, name: "Canon EOS R5", category: "Kamera", price: "Rp 45.999.000", rating: 4.9, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 10, name: "Sony Alpha A7 IV", category: "Kamera", price: "Rp 35.999.000", rating: 4.8, shipping: "Gratis Ongkir", isMall: true, cod: false },
];

// Simulasi displayed products (setelah filter price/rating/shipping/features)
const displayedProductsAfterPriceFilter = [
  // Hanya produk dengan harga < Rp 10.000.000
  { id: 1, name: "PlayStation 5", category: "Konsol Game", price: "Rp 7.999.000", rating: 4.8, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 2, name: "Xbox Series X", category: "Konsol Game", price: "Rp 7.499.000", rating: 4.7, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 3, name: "Nintendo Switch OLED", category: "Konsol Game", price: "Rp 4.999.000", rating: 4.9, shipping: "Gratis Ongkir", isMall: true, cod: true },
  { id: 4, name: "Steam Deck", category: "Konsol Game", price: "Rp 8.999.000", rating: 4.6, shipping: "Gratis Ongkir", isMall: false, cod: false },
  { id: 5, name: "PlayStation 4 Pro", category: "Konsol Game", price: "Rp 4.299.000", rating: 4.5, shipping: "Gratis Ongkir", isMall: true, cod: true },
  { id: 6, name: "Samsung Smart TV 55\"", category: "TV & Audio", price: "Rp 8.999.000", rating: 4.7, shipping: "Gratis Ongkir", isMall: true, cod: false },
  { id: 7, name: "Sony Soundbar", category: "TV & Audio", price: "Rp 2.999.000", rating: 4.6, shipping: "Gratis Ongkir", isMall: true, cod: true },
];

// Test scenarios
const testScenarios = [
  {
    name: 'Scenario 1: No filters applied',
    allProducts: allProducts,
    displayedProducts: allProducts,
    expectedSubcategoryCounts: {
      'Konsol Game': 5,
      'TV & Audio': 3,
      'Kamera': 2
    },
    expectedPriceRangeCounts: {
      'Rp 1.000.000 - Rp 5.000.000': 3,
      'Rp 5.000.000 - Rp 10.000.000': 4,
      'Di atas Rp 10.000.000': 3
    }
  },
  {
    name: 'Scenario 2: Price filter applied (< Rp 10.000.000)',
    allProducts: allProducts,
    displayedProducts: displayedProductsAfterPriceFilter,
    expectedSubcategoryCounts: {
      'Konsol Game': 5, // HARUS TETAP 5 (dari allProducts)
      'TV & Audio': 3,  // HARUS TETAP 3 (dari allProducts)
      'Kamera': 2       // HARUS TETAP 2 (dari allProducts)
    },
    expectedPriceRangeCounts: {
      'Rp 1.000.000 - Rp 5.000.000': 3, // Dari displayedProducts
      'Rp 5.000.000 - Rp 10.000.000': 4, // Dari displayedProducts
      'Di atas Rp 10.000.000': 0         // Dari displayedProducts (filtered out)
    }
  }
];

// Simulate facet calculation logic
function calculateFacetCounts(allProducts, displayedProducts) {
  // Subcategory counts - HARUS menggunakan allProducts
  const subcategoryCounts = {};
  allProducts.forEach(product => {
    if (product.category) {
      subcategoryCounts[product.category] = (subcategoryCounts[product.category] || 0) + 1;
    }
  });
  
  // Price range counts - menggunakan displayedProducts
  const priceRangeCounts = {
    'Rp 1.000.000 - Rp 5.000.000': 0,
    'Rp 5.000.000 - Rp 10.000.000': 0,
    'Di atas Rp 10.000.000': 0
  };
  
  displayedProducts.forEach(product => {
    const price = parseFloat(product.price.replace(/[^\d]/g, ''));
    if (price >= 1000000 && price < 5000000) {
      priceRangeCounts['Rp 1.000.000 - Rp 5.000.000']++;
    } else if (price >= 5000000 && price < 10000000) {
      priceRangeCounts['Rp 5.000.000 - Rp 10.000.000']++;
    } else if (price >= 10000000) {
      priceRangeCounts['Di atas Rp 10.000.000']++;
    }
  });
  
  return {
    subcategoryCounts,
    priceRangeCounts
  };
}

// Run tests
console.log('🧪 RUNNING TESTS:');
console.log('================');

testScenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}`);
  console.log('All products count:', scenario.allProducts.length);
  console.log('Displayed products count:', scenario.displayedProducts.length);
  
  const result = calculateFacetCounts(scenario.allProducts, scenario.displayedProducts);
  
  console.log('Results:');
  console.log('  Subcategory counts:', result.subcategoryCounts);
  console.log('  Price range counts:', result.priceRangeCounts);
  
  console.log('Expected:');
  console.log('  Subcategory counts:', scenario.expectedSubcategoryCounts);
  console.log('  Price range counts:', scenario.expectedPriceRangeCounts);
  
  // Verify subcategory counts (should be stable)
  const subcategoryMatch = JSON.stringify(result.subcategoryCounts) === JSON.stringify(scenario.expectedSubcategoryCounts);
  
  // Verify price range counts (should change based on displayed products)
  const priceRangeMatch = JSON.stringify(result.priceRangeCounts) === JSON.stringify(scenario.expectedPriceRangeCounts);
  
  if (subcategoryMatch && priceRangeMatch) {
    console.log('✅ TEST PASSED');
  } else {
    console.log('❌ TEST FAILED');
    console.log('  Subcategory match:', subcategoryMatch);
    console.log('  Price range match:', priceRangeMatch);
  }
  
  console.log('---');
});

console.log('\n🎯 SUMMARY:');
console.log('✅ Subcategory counts should be calculated from allProducts (stable)');
console.log('✅ Price/Rating/Shipping/Features counts should be calculated from displayedProducts (dynamic)');
console.log('🔄 When price filter applied:');
console.log('  - Subcategory counts remain the same (5 Konsol Game, 3 TV & Audio, 2 Kamera)');
console.log('  - Price range counts change based on filtered results');

console.log('\n📊 EXPECTED BEHAVIOR:');
console.log('1. User applies price filter "< Rp 10.000.000"');
console.log('2. Displayed products: 7 items (expensive items filtered out)');
console.log('3. Subcategory counts in facet panel: UNCHANGED');
console.log('   - Konsol Game (5) - shows total available, not filtered count');
console.log('   - TV & Audio (3) - shows total available, not filtered count');
console.log('   - Kamera (2) - shows total available, not filtered count');
console.log('4. Price range counts in facet panel: CHANGED');
console.log('   - Based on currently displayed products only');

console.log('\n🚫 WRONG BEHAVIOR (FIXED):');
console.log('❌ Subcategory counts changing when price/rating/shipping filters applied');
console.log('❌ Count showing filtered results instead of total available products');

console.log('\n✅ CORRECT BEHAVIOR (IMPLEMENTED):');
console.log('✅ Subcategory counts always show total products available in each subcategory');
console.log('✅ Other facet counts (price/rating/shipping/features) show counts from filtered results');
