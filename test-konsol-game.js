// Test script untuk memverifikasi data produk "Konsol Game"
// Jalankan di browser console untuk debugging

console.log('🎮 TESTING KONSOL GAME DATA');

// Simulasi import data produk
const testProducts = [
  {
    id: 7,
    name: "PlayStation 5",
    shortName: "PS5",
    category: "Konsol Game",
    price: "Rp 7.999.000",
    originalPrice: "Rp 8.999.000",
    discount: "11%",
    rating: "4.8",
    sold: "1250+",
    shipping: "Gratis Ongkir",
    image: "/api/placeholder/200/200",
    isMall: true,
    cod: false
  },
  {
    id: 8,
    name: "Xbox Series X",
    shortName: "Xbox Series X",
    category: "Konsol Game",
    price: "Rp 7.499.000",
    originalPrice: "Rp 8.499.000",
    discount: "12%",
    rating: "4.7",
    sold: "890+",
    shipping: "Gratis Ongkir",
    image: "/api/placeholder/200/200",
    isMall: true,
    cod: false
  },
  {
    id: 9,
    name: "Nintendo Switch OLED",
    shortName: "Switch OLED",
    category: "Konsol Game",
    price: "Rp 4.999.000",
    originalPrice: "Rp 5.499.000",
    discount: "9%",
    rating: "4.9",
    sold: "2100+",
    shipping: "Gratis Ongkir",
    image: "/api/placeholder/200/200",
    isMall: true,
    cod: true
  },
  {
    id: 10,
    name: "Steam Deck",
    shortName: "Steam Deck",
    category: "Konsol Game",
    price: "Rp 8.999.000",
    originalPrice: "Rp 9.999.000",
    discount: "10%",
    rating: "4.6",
    sold: "450+",
    shipping: "Gratis Ongkir",
    image: "/api/placeholder/200/200",
    isMall: false,
    cod: false
  },
  {
    id: 11,
    name: "PlayStation 4 Pro",
    shortName: "PS4 Pro",
    category: "Konsol Game",
    price: "Rp 4.299.000",
    originalPrice: "Rp 4.999.000",
    discount: "14%",
    rating: "4.5",
    sold: "780+",
    shipping: "Gratis Ongkir",
    image: "/api/placeholder/200/200",
    isMall: true,
    cod: true
  }
];

// Test filtering logic
function testKonsolGameFiltering() {
  console.log('📊 TESTING DATA:');
  console.log('Total products:', testProducts.length);
  
  const konsolGameProducts = testProducts.filter(p => p.category === 'Konsol Game');
  console.log('Konsol Game products:', konsolGameProducts.length);
  console.log('Konsol Game names:', konsolGameProducts.map(p => p.name));
  
  // Test filter dengan kategori "Konsol Game"
  const filters = { kategori: ['Konsol Game'] };
  console.log('🔍 TESTING FILTER:', filters);
  
  const filteredResults = testProducts.filter(product => {
    if (filters.kategori && filters.kategori.length > 0) {
      const matchesCategory = filters.kategori.some(filterCategory => {
        const filterCat = filterCategory.toLowerCase();
        const productCategory = product.category?.toLowerCase() || '';
        
        console.log('Checking:', {
          filterCategory,
          productCategory: product.category,
          exactMatch: productCategory === filterCat
        });
        
        return productCategory === filterCat;
      });
      
      return matchesCategory;
    }
    return true;
  });
  
  console.log('✅ FILTERED RESULTS:');
  console.log('Count:', filteredResults.length);
  console.log('Names:', filteredResults.map(p => p.name));
  
  // Test filter dengan kategori "Elektronik" + subkategori "Konsol Game"
  const combinedFilters = { kategori: ['Elektronik', 'Konsol Game'] };
  console.log('🔍 TESTING COMBINED FILTER:', combinedFilters);
  
  const combinedResults = testProducts.filter(product => {
    if (combinedFilters.kategori && combinedFilters.kategori.length > 0) {
      const matchesCategory = combinedFilters.kategori.some(filterCategory => {
        const filterCat = filterCategory.toLowerCase();
        const productCategory = product.category?.toLowerCase() || '';
        
        // Exact match untuk subkategori
        if (productCategory === filterCat) {
          return true;
        }
        
        // Jika filter adalah "Elektronik", cek apakah produk termasuk subkategori elektronik
        if (filterCategory === 'Elektronik') {
          const elektronikSubcategories = ['Konsol Game', 'Aksesoris Konsol', 'TV & Audio'];
          return elektronikSubcategories.some(sub => 
            productCategory === sub.toLowerCase()
          );
        }
        
        return false;
      });
      
      return matchesCategory;
    }
    return true;
  });
  
  console.log('✅ COMBINED FILTERED RESULTS:');
  console.log('Count:', combinedResults.length);
  console.log('Names:', combinedResults.map(p => p.name));
  
  return {
    totalProducts: testProducts.length,
    konsolGameProducts: konsolGameProducts.length,
    filteredByKonsolGame: filteredResults.length,
    filteredByCombined: combinedResults.length
  };
}

// Jalankan test
const testResult = testKonsolGameFiltering();
console.log('🎯 TEST SUMMARY:', testResult);

// Expected results:
// - totalProducts: 5
// - konsolGameProducts: 5
// - filteredByKonsolGame: 5
// - filteredByCombined: 5
