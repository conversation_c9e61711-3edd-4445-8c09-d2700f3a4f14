import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import "./sellzio-styles.css"

interface FacetData {
  categories: Record<string, number>
  priceRanges: Record<string, number>
  ratings: Record<string, number>
  shipping: Record<string, number>
  features: Record<string, number>
}

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
  // Tambahkan properti untuk facet filter
  kategori?: string[]
  'rentang harga'?: string[]
  rating?: string[]
  pengiriman?: string[]
  fitur?: string[]
}

interface SellzioFacetProps {
  searchResults: any[]
  displayedProducts?: any[] // Add this for calculating facet data from displayed products
  activeFilters: ActiveFilters
  onFiltersChange: (filters: ActiveFilters) => void
  isVisible: boolean
  onClose: () => void
  isDesktopSidebar?: boolean
  allProducts?: any[] // Add this to access all products for counting
  subcategoryContext?: {
    category: string
    selectedSubcategory: string
    allSubcategories: Array<{
      id: string
      name: string
      icon?: string
      color?: string
    }>
  } | null
}

export function SellzioFacet({
  searchResults,
  displayedProducts,
  activeFilters,
  onFiltersChange,
  isVisible,
  onClose,
  isDesktopSidebar = false,
  allProducts = [],
  subcategoryContext
}: SellzioFacetProps) {
  const [tempFilters, setTempFilters] = useState<ActiveFilters>(activeFilters)
  const [facetData, setFacetData] = useState<FacetData>({
    categories: {},
    priceRanges: {},
    ratings: {},
    shipping: {},
    features: {}
  })
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isSubcategoriesExpanded, setIsSubcategoriesExpanded] = useState(false)
  const [sortedSubcategories, setSortedSubcategories] = useState<string[]>([])
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null)

  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768) // Mobile: < 768px
      setIsTablet(width >= 768 && width < 1025) // Tablet: 768px - 1024px
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Extract facets from search results
  useEffect(() => {
    console.log('Facet data useEffect triggered - searchResults:', searchResults.length, 'displayedProducts:', displayedProducts?.length, 'subcategoryContext:', subcategoryContext);
    // Use displayedProducts if available, otherwise fallback to searchResults
    const productsForFacets = displayedProducts || searchResults;
    const facets = extractFacets(productsForFacets)
    setFacetData(facets)
    console.log('Set facet data:', facets);
  }, [searchResults, displayedProducts, subcategoryContext, tempFilters])

  // Reset temp filters when activeFilters change
  useEffect(() => {
    console.log('🎯 FACET: activeFilters changed, updating tempFilters:', activeFilters);
    setTempFilters({ ...activeFilters });
  }, [activeFilters])

  // Track previous subcategory to detect changes
  const [prevSubcategory, setPrevSubcategory] = useState<string | null>(null);

  // Initialize sorted subcategories when context changes (from subcategory view clicks)
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    if (context && context.allSubcategories) {
      const subcategoryNames = context.allSubcategories.map((sub: any) => sub.name);

      // Set selected subcategory if available (from subcategory view click)
      if (context.selectedSubcategory) {
        setSelectedSubcategory(context.selectedSubcategory);
        // Move selected to top ONLY when coming from subcategory view
        const reordered = [
          context.selectedSubcategory,
          ...subcategoryNames.filter((name: string) => name !== context.selectedSubcategory)
        ];
        setSortedSubcategories(reordered);
        console.log('🎯 FACET: Reordered from subcategory view click:', context.selectedSubcategory);
      } else {
        // No selection from subcategory view, use original order
        setSortedSubcategories(subcategoryNames);
        setSelectedSubcategory(null);
        console.log('🎯 FACET: Using original subcategory order');
      }
    }
  }, [subcategoryContext]);

  // Reset temp filters when subcategory context changes (new subcategory selected)
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    const currentSubcategory = context?.selectedSubcategory;

    if (currentSubcategory && currentSubcategory !== prevSubcategory) {
      console.log('🎯 FACET: Subcategory context changed from', prevSubcategory, 'to', currentSubcategory);
      // Reset to only show the newly selected subcategory
      const newFilters = {
        kategori: [currentSubcategory]
      };
      setTempFilters(newFilters);
      setPrevSubcategory(currentSubcategory);
    }
  }, [subcategoryContext, prevSubcategory])

  // Separate effect to apply filters for desktop sidebar
  useEffect(() => {
    if (isDesktopSidebar && tempFilters.kategori) {
      const timeoutId = setTimeout(() => {
        onFiltersChange(tempFilters);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [tempFilters, isDesktopSidebar, onFiltersChange])

  const extractFacets = (results: any[]): FacetData => {
    // Use subcategory context from props or window
    const context = subcategoryContext || (window as any).subcategoryContext;
    console.log('🔥 FACET DEBUG: extractFacets called');
    console.log('🔥 FACET DEBUG: results length:', results.length);
    console.log('🔥 FACET DEBUG: allProducts length:', allProducts.length);
    console.log('🔥 FACET DEBUG: context:', context);

    // FIXED: Use search results directly - no hardcoded data
    console.log('🔥 FACET DEBUG: Using search results directly:', searchResults.length, 'products');
    console.log('🔥 FACET DEBUG: Search results data:', searchResults.map(p => `${p.name} (${p.category})`));

    // Check if any subcategory is selected in current filters
    const hasSubcategorySelected = context && context.allSubcategories?.some((sub: any) =>
      tempFilters.kategori?.includes(sub.name)
    );

    // Always show all subcategories if we have context
    const shouldShowAllSubcategories = context && context.allSubcategories;

    // Jika tidak ada hasil atau dalam konteks subkategori, berikan data sample untuk demo
    if (shouldShowAllSubcategories) {
      // If we have subcategory context, use it for categories
      let categories = {
        "Handphone & Tablet": 45,
        "Elektronik": 32,
        "Fashion Pria": 28,
        "Fashion Wanita": 41,
        "Tas & Travel": 19,
        "Sepatu": 23,
        "Aksesoris Fashion": 15
      };

      // Override with subcategory data if available
      if (context && context.allSubcategories && context.allSubcategories.length > 0) {
        console.log('🔥 FACET: Creating categories from subcategories:', context.allSubcategories);
        console.log('🔥 FACET: Selected subcategory:', context.selectedSubcategory);
        console.log('🔥 FACET: Category:', context.category);
        console.log('🔥 FACET: All subcategories count:', context.allSubcategories.length);
        console.log('🔥 FACET: Has subcategory selected:', hasSubcategorySelected);

        const dynamicCategories: { [key: string]: number } = {};

        // Calculate actual product counts for each subcategory based on ALL products (not filtered results)
        let totalCategoryCount = 0;
        context.allSubcategories.forEach((sub: any) => {
          // FIXED: Use allProducts for subcategory counting to avoid filter interference
          const productsToCount = allProducts.length > 0 ? allProducts : searchResults;
          console.log(`🔍 FACET DEBUG: Subcategory "${sub.name}" - using UNFILTERED PRODUCTS:`, productsToCount.length);
          console.log(`🔍 FACET DEBUG: Source:`, allProducts.length > 0 ? 'allProducts' : 'searchResults (fallback)');
          console.log(`🔍 FACET DEBUG: Unfiltered products:`, productsToCount.slice(0, 3).map(p => `${p.name} (${p.category})`), '...');

          const subcategoryCount = productsToCount.filter((product: any) => {
            const subName = sub.name.toLowerCase();
            const productName = product.name?.toLowerCase() || '';
            const productCategory = product.category?.toLowerCase() || '';

            // FIXED: Use ONLY exact category matching to prevent double counting
            // Remove enhanced matching to avoid counting same product twice
            return productCategory === subName;

            if (subName.includes('casing') || subName.includes('case')) {
              if (productName.includes('case') || productName.includes('casing') ||
                  productName.includes('cover') || productName.includes('housing')) return true;
            }

            if (subName.includes('foot bath') || subName.includes('spa')) {
              if (productName.includes('foot') || productName.includes('spa') ||
                  productName.includes('bath') || productName.includes('massage')) return true;
            }

            if (subName.includes('mesin jahit')) {
              if (productName.includes('sewing') || productName.includes('jahit') ||
                  productName.includes('mesin')) return true;
            }

            if (subName.includes('setrika') || subName.includes('mesin uap')) {
              if (productName.includes('iron') || productName.includes('setrika') ||
                  productName.includes('steam') || productName.includes('uap')) return true;
            }

            if (subName.includes('purifier') || subName.includes('humidifier')) {
              if (productName.includes('purifier') || productName.includes('humidifier') ||
                  productName.includes('air') || productName.includes('filter')) return true;
            }

            if (subName.includes('telepon')) {
              if (productName.includes('phone') || productName.includes('telepon') ||
                  productName.includes('telephone')) return true;
            }

            if (subName.includes('cuci') || subName.includes('pengering')) {
              if (productName.includes('wash') || productName.includes('cuci') ||
                  productName.includes('dryer') || productName.includes('pengering')) return true;
            }

            return false;
          }).length;

          // Show all subcategories with their actual product count (even if 0)
          const count = subcategoryCount;
          dynamicCategories[sub.name] = count;
          totalCategoryCount += count;
          console.log(`✅ FACET: Added subcategory: ${sub.name} with actual count: ${count}`);
          console.log(`🔍 FACET: Products used for subcategory counting (all category):`, productsToCount.length);

          // Debug: Show which products matched for this subcategory
          if (sub.name === "Konsol Game" || sub.name === "Aksesoris Konsol") {
            const matchedProducts = productsToCount.filter((product: any) => {
              const subName = sub.name.toLowerCase();
              const productCategory = product.category?.toLowerCase() || '';

              // FIXED: Use ONLY exact category matching to prevent double counting
              return productCategory === subName;
            });
            console.log(`🎮 FACET: ${sub.name} matched products:`, matchedProducts.map(p => `${p.name} (${p.category})`));
          }
        });

        // Add main category with total count from all subcategories
        dynamicCategories[context.category] = totalCategoryCount;
        console.log(`✅ FACET: Added main category: ${context.category} with count: ${totalCategoryCount}`);

        categories = dynamicCategories as typeof categories;
        console.log('✅ FACET: Final categories object:', categories);
      } else {
        console.log('❌ FACET: No subcategory context found');
        console.log('❌ FACET: Context:', context);
        console.log('❌ FACET: subcategoryContext prop:', subcategoryContext);
        console.log('❌ FACET: window context:', (window as any).subcategoryContext);

        // Fallback: Check if we're in Elektronik category based on search results
        const hasElektronikProducts = searchResults.some(product =>
          product.category && product.category.toLowerCase().includes('konsol')
        );

        if (hasElektronikProducts) {
          console.log('🔥 FACET: Detected Elektronik category, adding all subcategories');
          const elektronikSubcategories = [
            "Konsol Game", "Aksesoris Konsol", "Alat Casing", "Foot Bath & Spa",
            "Mesin Jahit & Aksesoris", "Setrika & Mesin Uap", "Purifier & Humidifier",
            "Perangkat Debu & Peralatan Perawatan Lantai", "Telepon", "Mesin Cuci & Pengering",
            "Water Heater", "Pendingin Ruangan", "Pengering Sepatu", "Penghangat Ruangan",
            "TV & Aksesoris", "Perangkat Dapur", "Lampu", "Kamera Keamanan",
            "Video Game", "Kelastrian", "Baterai", "Rokok Elektronik & Shisha",
            "Remote Kontrol", "Walkie Talkie", "Media Player", "Perangkat Audio & Speaker",
            "Elektronik Lainnya"
          ];

          const dynamicCategories: { [key: string]: number } = {};

          // Add main category - calculate from actual subcategory counts
          let totalElektronikCount = 0;

          // Add all subcategories with real counts
          elektronikSubcategories.forEach((subName) => {
            // FIXED: Use allProducts for subcategory counting to avoid filter interference
            const productsToCount = allProducts.length > 0 ? allProducts : searchResults;
            console.log(`🔍 FACET FALLBACK: Subcategory "${subName}" - using UNFILTERED PRODUCTS:`, productsToCount.length);
            console.log(`🔍 FACET FALLBACK: Source:`, allProducts.length > 0 ? 'allProducts' : 'searchResults (fallback)');
            const subcategoryCount = productsToCount.filter((product: any) => {
              const subNameLower = subName.toLowerCase();
              const productCategory = product.category?.toLowerCase() || '';

              // FIXED: Use ONLY exact category matching to prevent double counting
              return productCategory === subNameLower;
            }).length;

            dynamicCategories[subName] = subcategoryCount;
            totalElektronikCount += subcategoryCount;
            console.log(`🔍 FACET FALLBACK: Added subcategory: ${subName} with count: ${subcategoryCount}`);
          });

          // Set main category count as sum of all subcategories
          dynamicCategories["Elektronik"] = totalElektronikCount;

          categories = dynamicCategories as typeof categories;
          console.log('🔥 FACET: Added Elektronik subcategories:', Object.keys(dynamicCategories));
        }
      }

      // FIXED: Calculate real data for all facets based on displayed products
      const realFacets = {
        priceRanges: {
          "Di bawah Rp 100.000": 0,
          "Rp 100.000 - Rp 500.000": 0,
          "Rp 500.000 - Rp 1.000.000": 0,
          "Rp 1.000.000 - Rp 5.000.000": 0,
          "Di atas Rp 5.000.000": 0
        },
        ratings: {
          "5 Bintang": 0,
          "4 Bintang ke atas": 0,
          "3 Bintang ke atas": 0
        },
        shipping: {
          "Gratis Ongkir": 0,
          "Same Day": 0,
          "Next Day": 0
        },
        features: {
          "COD": 0,
          "SellZio Mall": 0,
          "Flash Sale": 0
        }
      };

      // Calculate real counts from displayed products (for price, rating, shipping, features only)
      // Note: results parameter here is already displayedProducts from useEffect
      results.forEach(product => {
        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          realFacets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          realFacets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          realFacets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          realFacets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          realFacets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) realFacets.ratings["5 Bintang"]++
        if (rating >= 4) realFacets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) realFacets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") realFacets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") realFacets.shipping["Same Day"]++
        if (product.shipping === "Next Day") realFacets.shipping["Next Day"]++

        // Features
        if (product.cod === true) realFacets.features["COD"]++
        if (product.isMall === true) realFacets.features["SellZio Mall"]++
        if (product.flashSale === true) realFacets.features["Flash Sale"]++
      });

      console.log('🔥 FACET: ✅ MIXED DATA SOURCES:');
      console.log('🔥 FACET: ✅ Categories calculated from:', searchResults.length, 'ALL CATEGORY products (for complete subcategory list)');
      console.log('🔥 FACET: ✅ Price ranges calculated from:', results.length, 'DISPLAYED products:', realFacets.priceRanges);
      console.log('🔥 FACET: ✅ Ratings calculated from:', results.length, 'DISPLAYED products:', realFacets.ratings);
      console.log('🔥 FACET: ✅ Shipping calculated from:', results.length, 'DISPLAYED products:', realFacets.shipping);
      console.log('🔥 FACET: ✅ Features calculated from:', results.length, 'DISPLAYED products:', realFacets.features);
      console.log('🔥 FACET: ✅ Displayed products list:', results.map(p => `${p.name} (${p.category}) - ${p.price}`));

      return {
        categories,
        priceRanges: realFacets.priceRanges,
        ratings: realFacets.ratings,
        shipping: realFacets.shipping,
        features: realFacets.features
      }
    }

    const facets: FacetData = {
      categories: {},
      priceRanges: {
        "Di bawah Rp 100.000": 0,
        "Rp 100.000 - Rp 500.000": 0,
        "Rp 500.000 - Rp 1.000.000": 0,
        "Rp 1.000.000 - Rp 5.000.000": 0,
        "Di atas Rp 5.000.000": 0
      },
      ratings: {
        "5 Bintang": 0,
        "4 Bintang ke atas": 0,
        "3 Bintang ke atas": 0
      },
      shipping: {
        "Gratis Ongkir": 0,
        "Same Day": 0,
        "Next Day": 0
      },
      features: {
        "COD": 0,
        "SellZio Mall": 0,
        "Flash Sale": 0
      }
    }

    // FIXED: Only process for regular search (not subcategory context) to avoid double counting
    if (!context || !context.allSubcategories) {
      results.forEach(product => {
        // Categories - enhanced mapping for consistent structure
        if (product.category) {
          // Map subcategories to main categories for consistent structure
          const categoryMappings = {
            'Elektronik': ['konsol game', 'aksesoris gaming', 'handphone & tablet', 'komputer & laptop',
                          'audio', 'kamera', 'electronic gadget', 'aksesoris elektronik', 'aksesoris komputer',
                          'video game', 'kelastrian', 'baterai', 'rokok elektronik & shisha',
                          'remote kontrol', 'walkie talkie', 'media player', 'perangkat audio & speaker',
                          'elektronik lainnya', 'alat casing', 'foot bath & spa', 'mesin jahit & aksesoris',
                          'setrika & mesin uap', 'purifier & humidifier', 'perangkat debu & peralatan perawatan lantai',
                          'telepon', 'mesin cuci & pengering', 'water heater', 'pendingin ruangan',
                          'pengering sepatu', 'penghangat ruangan', 'tv & aksesoris', 'perangkat dapur',
                          'lampu', 'kamera keamanan', 'aksesoris konsol'],
            'Komputer & Aksesoris': ['laptop', 'komputer', 'aksesoris komputer', 'software', 'monitor', 'keyboard', 'mouse'],
            'Handphone & Aksesoris': ['handphone', 'smartphone', 'aksesoris handphone', 'tablet', 'case handphone', 'charger'],
            'Pakaian Pria': ['kemeja', 'celana', 'jaket', 'kaos', 'pakaian dalam', 'sweater', 'hoodie'],
            'Sepatu Pria': ['sepatu formal', 'sepatu casual', 'sepatu olahraga', 'sandal', 'boots'],
            'Tas Pria': ['tas kerja', 'tas casual', 'dompet', 'ransel', 'tas laptop', 'tas travel'],
            'Aksesoris Fashion': ['jam tangan', 'kacamata', 'topi', 'ikat pinggang', 'kalung', 'gelang'],
            'Jam Tangan': ['jam tangan pria', 'jam tangan wanita', 'smartwatch', 'jam digital', 'jam analog'],
            'Kesehatan': ['vitamin', 'suplemen', 'alat kesehatan', 'obat-obatan', 'masker', 'hand sanitizer'],
            'Hobi & Koleksi': ['mainan', 'koleksi', 'buku', 'alat musik', 'puzzle', 'board game']
          };

          const categoryLower = product.category.toLowerCase();
          let mainCategory = 'Lainnya';

          // Find main category for this subcategory
          for (const [main, subs] of Object.entries(categoryMappings)) {
            if (subs.some(sub => categoryLower.includes(sub) || sub.includes(categoryLower))) {
              mainCategory = main;
              break;
            }
          }

          // Count both main category and subcategory
          facets.categories[mainCategory] = (facets.categories[mainCategory] || 0) + 1;
          facets.categories[product.category] = (facets.categories[product.category] || 0) + 1;
        }

        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          facets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          facets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          facets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          facets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) facets.ratings["5 Bintang"]++
        if (rating >= 4) facets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) facets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") facets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") facets.shipping["Same Day"]++
        if (product.shipping === "Next Day") facets.shipping["Next Day"]++

        // Features
        if (product.cod === true) facets.features["COD"]++
        if (product.isMall === true) facets.features["SellZio Mall"]++
        if (product.flashSale === true) facets.features["Flash Sale"]++
      })
    }

    // Categories are already handled in the first system above when we have subcategory context
    // No need to add missing subcategories here as they're already calculated with real counts

    return facets
  }

  const handleFilterChange = (type: keyof ActiveFilters, value: string, checked: boolean) => {
    console.log('🎯 FACET: Filter change:', { type, value, checked });

    setTempFilters(prev => {
      const newFilters = { ...prev }
      if (!newFilters[type]) newFilters[type] = []

      // Get context for category handling
      const context = subcategoryContext || (window as any).subcategoryContext;
      const isKategoriType = type === 'kategori';

      if (checked) {
        if (!newFilters[type]!.includes(value)) {
          newFilters[type]!.push(value)
        }

        // ADDED: Auto-check main category when subcategory is selected
        if (isKategoriType && context && context.allSubcategories) {
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);

          if (isSubcategory) {
            // Auto-check main category when subcategory is selected
            if (!newFilters[type]!.includes(context.category)) {
              newFilters[type]!.push(context.category);
              console.log('🎯 FACET: Auto-checked main category:', context.category);
            }

            // REMOVED: Don't reorder when checking in facet panel
            // Reordering should only happen when clicking subcategory in main view
            console.log('🎯 FACET: Subcategory checked in facet panel (no reordering):', value);
          }
        }
      } else {
        // Handle unchecking
        if (isKategoriType && context && context.allSubcategories) {
          const isMainCategory = value === context.category;
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);

          // Prevent unchecking main category if any subcategory is still selected
          if (isMainCategory) {
            const hasSelectedSubcategories = context.allSubcategories.some((sub: any) =>
              newFilters[type]!.includes(sub.name)
            );

            if (hasSelectedSubcategories) {
              console.log('🚫 FACET: Cannot uncheck main category - subcategories still selected');
              return prev; // Don't allow unchecking main category
            }
          }

          // If unchecking subcategory, check if it's the last one
          if (isSubcategory) {
            const remainingSubcategories = context.allSubcategories.filter((sub: any) =>
              sub.name !== value && newFilters[type]!.includes(sub.name)
            );

            // If no more subcategories selected, also uncheck main category
            if (remainingSubcategories.length === 0) {
              newFilters[type] = newFilters[type]!.filter(item => item !== context.category);
              console.log('🎯 FACET: Auto-unchecked main category - no subcategories left');
            }
          }
        }

        newFilters[type] = newFilters[type]!.filter(item => item !== value)

        if (newFilters[type]!.length === 0) {
          delete newFilters[type]
        }
      }

      console.log('🎯 FACET: New filters:', newFilters);

      // Auto apply filters for desktop sidebar immediately
      if (isDesktopSidebar) {
        // Apply filters immediately for desktop
        setTimeout(() => {
          console.log('🎯 FACET: Applying filters to parent:', newFilters);
          onFiltersChange(newFilters)
        }, 0)
      }

      return newFilters
    })
  }



  const applyFilters = () => {
    onFiltersChange(tempFilters)
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const resetFilters = () => {
    setTempFilters({})
    onFiltersChange({})
  }

  const countActiveFilters = () => {
    return Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)
  }

  const renderFacetSection = (title: string, items: Record<string, number>, type: keyof ActiveFilters) => {
    const hasItems = Object.keys(items).some(key => items[key] > 0)
    if (!hasItems) return null

    // Check if this is kategori section
    const context = subcategoryContext || (window as any).subcategoryContext;
    const isKategoriSection = type === 'kategori';

    // FIXED: Always show hierarchical structure for categories, whether from subcategory context or regular search
    const shouldShowHierarchical = isKategoriSection && (
      (context && context.allSubcategories) || // From subcategory context
      Object.keys(items).some(key => { // Or from regular search with main categories
        const mainCategories = ['Elektronik', 'Komputer & Aksesoris', 'Handphone & Aksesoris',
                               'Pakaian Pria', 'Sepatu Pria', 'Tas Pria', 'Aksesoris Fashion',
                               'Jam Tangan', 'Kesehatan', 'Hobi & Koleksi'];
        return mainCategories.includes(key);
      })
    );

    return (
      <div className="facet-section">
        <h3>{title}</h3>
        <ul>
          {shouldShowHierarchical ? (
            // Render category name first, then subcategories with indentation
            <>
              {/* Category name with checkbox - calculate total count */}
              {(() => {
                const categoryCount = items[context.category] || 0;
                // Auto-check main category if any subcategory is selected
                const hasSelectedSubcategories = context.allSubcategories?.some((sub: any) =>
                  tempFilters[type]?.includes(sub.name)
                ) || false;
                const isCategoryChecked = tempFilters[type]?.includes(context.category) || hasSelectedSubcategories;
                const categoryCheckboxId = `facet-${type}-${context.category.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                return (
                  <li>
                    <input
                      type="checkbox"
                      id={categoryCheckboxId}
                      className="orange-checkbox"
                      checked={isCategoryChecked}
                      disabled={hasSelectedSubcategories} // Disable when subcategories are selected
                      onChange={(e) => {
                        console.log('🎯 FACET: Main category checkbox changed:', {
                          category: context.category,
                          checked: e.target.checked,
                          hasSelectedSubcategories
                        });
                        if (!hasSelectedSubcategories) {
                          handleFilterChange(type, context.category, e.target.checked)
                        }
                      }}
                      data-facet-type={type}
                      data-facet-value={context.category}
                    />
                    <label htmlFor={categoryCheckboxId} className={`category-name ${hasSelectedSubcategories ? 'disabled' : ''}`}>
                      {context.category} ({categoryCount})
                    </label>
                  </li>
                );
              })()}

              {/* Subcategories with indentation and checkboxes */}
              {(() => {
                // Get subcategories in sorted order (selected first)
                const subcategoryEntries = Object.entries(items).filter(([key]) => key !== context.category);

                // Use sorted order if available, otherwise use original order
                let orderedSubcategories = subcategoryEntries;
                if (sortedSubcategories.length > 0) {
                  orderedSubcategories = sortedSubcategories
                    .map(name => subcategoryEntries.find(([key]) => key === name))
                    .filter(Boolean) as [string, number][];

                  // Add any missing subcategories at the end
                  const missingSubcategories = subcategoryEntries.filter(([key]) =>
                    !sortedSubcategories.includes(key)
                  );
                  orderedSubcategories = [...orderedSubcategories, ...missingSubcategories];
                }

                // For desktop sidebar, show only 5 items initially with expand button
                const isDesktopSidebar = !isMobile && !isTablet;
                const maxInitialItems = 5;
                const shouldShowExpandButton = isDesktopSidebar && orderedSubcategories.length > maxInitialItems;
                const itemsToShow = shouldShowExpandButton && !isSubcategoriesExpanded
                  ? orderedSubcategories.slice(0, maxInitialItems)
                  : orderedSubcategories;

                return (
                  <>
                    {itemsToShow.map(([key, count]) => {
                      const isChecked = tempFilters[type]?.includes(key) || false
                      const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                      return (
                        <li key={key} className="subcategory-item">
                          <input
                            type="checkbox"
                            id={checkboxId}
                            className="orange-checkbox"
                            checked={isChecked}
                            onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                            data-facet-type={type}
                            data-facet-value={key}
                            disabled={count === 0}
                          />
                          <label htmlFor={checkboxId}>
                            {key} ({count})
                          </label>
                        </li>
                      )
                    })}

                    {/* Expand/Collapse button for desktop */}
                    {shouldShowExpandButton && (
                      <li className="subcategory-expand-button">
                        <button
                          className="expand-button"
                          onClick={() => setIsSubcategoriesExpanded(!isSubcategoriesExpanded)}
                        >
                          {isSubcategoriesExpanded ? (
                            <>
                              <span>Tampilkan Lebih Sedikit</span>
                              <span className="expand-arrow up">▲</span>
                            </>
                          ) : (
                            <>
                              <span>Tampilkan {orderedSubcategories.length - maxInitialItems} Lainnya</span>
                              <span className="expand-arrow down">▼</span>
                            </>
                          )}
                        </button>
                      </li>
                    )}
                  </>
                );
              })()}
            </>
          ) : (
            // Regular rendering for other sections
            Object.entries(items).map(([key, count]) => {
              // Show all items regardless of count
              const isChecked = tempFilters[type]?.includes(key) || false
              const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

              return (
                <li key={key}>
                  <input
                    type="checkbox"
                    id={checkboxId}
                    className="orange-checkbox"
                    checked={isChecked}
                    onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                    data-facet-type={type}
                    data-facet-value={key}
                    disabled={count === 0}
                  />
                  <label htmlFor={checkboxId}>
                    {key} ({count})
                  </label>
                </li>
              )
            })
          )}
        </ul>
      </div>
    )
  }



  // For desktop sidebar, always show when isDesktopSidebar is true
  // For mobile/tablet popup, only show when isVisible is true
  if (!isDesktopSidebar && !isVisible) return null

  // Desktop Sidebar Layout
  if (isDesktopSidebar) {
    return (
      <div className="facet-sidebar-desktop">
        <div className="facet-header">
          <div className="facet-title">
            <Filter size={18} className="facet-filter-icon" />
            Filter
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    )
  }

  // Mobile/Tablet Popup Layout
  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <div className="facet-overlay" style={{ display: (isMobile || isTablet) ? 'flex' : 'none' }}>
        <div className="facet-panel">
          <div className="facet-header">
            <div className="facet-title">Filter</div>
            <div className="facet-close" onClick={onClose}>
              <X size={18} />
            </div>
          </div>

          <div className="facet-content-wrapper">
            <div className="facet-content">
              {renderFacetSection('Kategori', facetData.categories, 'kategori')}
              {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
              {renderFacetSection('Rating', facetData.ratings, 'rating')}
              {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
              {renderFacetSection('Fitur', facetData.features, 'fitur')}
            </div>
          </div>

          <div className="facet-buttons">
            <div className="facet-button facet-button-reset" onClick={resetFilters}>
              Reset
            </div>
            <div className="facet-button facet-button-apply" onClick={applyFilters}>
              Terapkan ({countActiveFilters()})
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Panel - Only for non-sidebar mode */}
      <div
        className="facet-panel-desktop"
        style={{ display: (!isMobile && !isTablet) ? 'block' : 'none' }}
      >
        <div className="facet-header">
          <div className="facet-title">Filter</div>
          <div className="facet-close" onClick={onClose}>
            <X size={18} />
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    </>
  )
}